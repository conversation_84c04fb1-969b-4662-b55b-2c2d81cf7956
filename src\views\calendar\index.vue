<template>
  <div class="calendar-container">
    <div class="calendar-header">
      <el-button type="primary" @click="toggleFullScreen">
        <svg-icon :icon-class="isFullscreen ? 'exit-fullscreen' : 'fullscreen'" />
        {{ isFullscreen ? '退出全屏' : '全屏' }}
      </el-button>
    </div>
    <div ref="calendarContent" class="calendar-content">
      <FullCalendar
        ref="fullCalendar"
        :options="calendarOptions"
      />
    </div>
  </div>
</template>

<script>
import FullCalendar from '@fullcalendar/vue'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin from '@fullcalendar/interaction'
import screenfull from 'screenfull'

export default {
  name: 'FullCalendarPage',
  components: {
    FullCalendar
  },
  data() {
    return {
      isFullscreen: false,
      calendarOptions: {
        plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
        header: {
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        defaultView: 'dayGridMonth',
        editable: true,
        selectable: true,
        selectMirror: true,
        dayMaxEvents: true,
        weekends: true,
        locale: 'zh-cn',
        events: [
          { title: '示例事件1', start: new Date().toISOString().slice(0, 10) },
          {
            title: '示例事件2',
            start: new Date(new Date().setDate(new Date().getDate() + 1)).toISOString().slice(0, 10),
            end: new Date(new Date().setDate(new Date().getDate() + 3)).toISOString().slice(0, 10)
          }
        ],
        eventClick: this.handleEventClick,
        select: this.handleDateSelect
      }
    }
  },
  mounted() {
    if (screenfull.enabled) {
      screenfull.on('change', this.onFullscreenChange)
    }
  },
  beforeDestroy() {
    // 安全地销毁 FullCalendar 实例
    if (this.$refs.fullCalendar && this.$refs.fullCalendar.getApi) {
      try {
        const calendarApi = this.$refs.fullCalendar.getApi()
        if (calendarApi && typeof calendarApi.destroy === 'function') {
          calendarApi.destroy()
        }
      } catch (error) {
        console.warn('FullCalendar destroy error:', error)
      }
    }

    // 清理 screenfull 事件监听器
    if (screenfull.enabled) {
      screenfull.off('change', this.onFullscreenChange)
    }
  },
  methods: {
    toggleFullScreen() {
      if (!screenfull.enabled) {
        this.$message({
          message: '您的浏览器不支持全屏功能',
          type: 'warning'
        })
        return false
      }

      if (this.isFullscreen) {
        screenfull.exit()
      } else {
        screenfull.request(this.$el)
      }
    },
    onFullscreenChange() {
      this.isFullscreen = screenfull.isFullscreen
    },
    handleEventClick(info) {
      this.$alert(`事件: ${info.event.title}`, '事件详情', {
        confirmButtonText: '确定'
      })
    },
    handleDateSelect(info) {
      this.$prompt('请输入事件标题:', '添加新事件', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        if (value) {
          const calendarApi = info.view.calendar
          calendarApi.unselect() // 清除日期选择
          calendarApi.addEvent({
            title: value,
            start: info.startStr,
            end: info.endStr,
            allDay: info.allDay
          })
          this.$message({
            type: 'success',
            message: '事件添加成功'
          })
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消添加事件'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  .calendar-header {
    padding: 10px;
    display: flex;
    justify-content: flex-end;
  }
  .calendar-content {
    flex: 1;
    padding: 0 10px 10px;
  }
}

:deep(.fc) {
  height: 100%;
}

:deep(.fc-header-toolbar) {
  margin-bottom: 10px !important;
}

:fullscreen .calendar-container {
  padding: 20px;
  background-color: white;
}
</style>