<template>
  <div class="calendar-container">
    <div class="calendar-header">
      <el-button type="primary" @click="toggleFullScreen">
        {{ isFullscreen ? '退出全屏' : '全屏' }}
      </el-button>
      <span style="margin-left: 20px;">状态: {{ calendarStatus }}</span>
    </div>
    <div class="calendar-content">
      <div id="calendar-wrapper" ref="calendarWrapper">
        <!-- FullCalendar 将在这里渲染 -->
      </div>
    </div>
  </div>
</template>

<script>
import { Calendar } from '@fullcalendar/core'
import dayGridPlugin from '@fullcalendar/daygrid'
import timeGridPlugin from '@fullcalendar/timegrid'
import interactionPlugin from '@fullcalendar/interaction'
import screenfull from 'screenfull'

export default {
  name: 'FullCalendarPage',
  data() {
    return {
      isFullscreen: false,
      calendar: null,
      calendarStatus: '初始化中...'
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initCalendar()
    })
    if (screenfull.enabled) {
      screenfull.on('change', this.onFullscreenChange)
    }
  },
  beforeDestroy() {
    // 清理日历实例
    if (this.calendar) {
      this.calendar.destroy()
    }
    // 清理 screenfull 事件监听器
    if (screenfull.enabled) {
      screenfull.off('change', this.onFullscreenChange)
    }
  },
  methods: {
    initCalendar() {
      this.calendarStatus = '查找DOM元素...'
      const calendarEl = document.getElementById('calendar-wrapper')

      if (!calendarEl) {
        console.error('Calendar element not found!')
        this.calendarStatus = '错误: DOM元素未找到'
        return
      }

      this.calendarStatus = '创建日历实例...'
      console.log('Initializing calendar...')

      try {
        this.calendar = new Calendar(calendarEl, {
        plugins: [dayGridPlugin, timeGridPlugin, interactionPlugin],
        headerToolbar: {
          left: 'prev,next today',
          center: 'title',
          right: 'dayGridMonth,timeGridWeek,timeGridDay'
        },
        initialView: 'dayGridMonth',
        editable: true,
        selectable: true,
        selectMirror: true,
        dayMaxEvents: true,
        weekends: true,
        locale: 'zh-cn',
        height: 'auto',
        events: [
          {
            title: '示例事件1',
            start: this.getTodayString()
          },
          {
            title: '示例事件2',
            start: this.getTomorrowString(),
            end: this.getAfterTomorrowString()
          }
        ],
        eventClick: this.handleEventClick,
        select: this.handleDateSelect
      })

      this.calendar.render()
      this.calendarStatus = '日历加载成功!'
      console.log('Calendar rendered successfully!')
      } catch (error) {
        console.error('Calendar initialization failed:', error)
        this.calendarStatus = '错误: ' + error.message
      }
    },
    getTodayString() {
      return new Date().toISOString().slice(0, 10)
    },
    getTomorrowString() {
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      return tomorrow.toISOString().slice(0, 10)
    },
    getAfterTomorrowString() {
      const afterTomorrow = new Date()
      afterTomorrow.setDate(afterTomorrow.getDate() + 3)
      return afterTomorrow.toISOString().slice(0, 10)
    },
    toggleFullScreen() {
      if (!screenfull.enabled) {
        this.$message({
          message: '您的浏览器不支持全屏功能',
          type: 'warning'
        })
        return false
      }

      if (this.isFullscreen) {
        screenfull.exit()
      } else {
        screenfull.request(this.$el)
      }
    },
    onFullscreenChange() {
      this.isFullscreen = screenfull.isFullscreen
      // 全屏状态改变时重新渲染日历
      this.$nextTick(() => {
        if (this.calendar) {
          this.calendar.updateSize()
        }
      })
    },
    handleEventClick(info) {
      this.$alert(`事件: ${info.event.title}`, '事件详情', {
        confirmButtonText: '确定'
      })
    },
    handleDateSelect(info) {
      this.$prompt('请输入事件标题:', '添加新事件', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      }).then(({ value }) => {
        if (value) {
          this.calendar.unselect()
          this.calendar.addEvent({
            title: value,
            start: info.startStr,
            end: info.endStr,
            allDay: info.allDay
          })
          this.$message({
            type: 'success',
            message: '事件添加成功'
          })
        }
      }).catch(() => {
        this.$message({
          type: 'info',
          message: '取消添加事件'
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.calendar-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  .calendar-header {
    padding: 10px;
    display: flex;
    justify-content: flex-end;
  }

  .calendar-content {
    flex: 1;
    padding: 0 10px 10px;
    min-height: 600px;
  }
}

// FullCalendar 样式覆盖
::v-deep .fc {
  height: 100% !important;
}

::v-deep .fc-header-toolbar {
  margin-bottom: 10px !important;
}

::v-deep .fc-daygrid-event {
  border-radius: 3px;
}

::v-deep .fc-event-title {
  font-weight: normal;
}

// 全屏模式样式
:fullscreen .calendar-container {
  padding: 20px;
  background-color: white;
}
</style>